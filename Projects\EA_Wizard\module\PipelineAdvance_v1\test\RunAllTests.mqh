//+------------------------------------------------------------------+
//|                                                RunAllTests.mqh  |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "unit/TestCompositePipeline.mqh"
#include "integration/SimpleTestRunner.mqh"

//+------------------------------------------------------------------+
//| 字符串重複函數                                                     |
//+------------------------------------------------------------------+
string StringRepeat(string str, int count)
{
    string result = "";
    for(int i = 0; i < count; i++)
    {
        result = result + str;
    }
    return result;
}

//+------------------------------------------------------------------+
//| 運行所有 PipelineAdvance_v1 測試                                  |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1Tests()
{
    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件");
    Print(StringRepeat("=", 70));
    Print("開始時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    Print("\n" + StringRepeat("=", 70));
    Print("  PipelineAdvance_v1 完整測試套件完成");
    Print("結束時間: " + TimeToString(TimeCurrent()));
    Print(StringRepeat("=", 70));
}

//+------------------------------------------------------------------+
//| 運行單元測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1UnitTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 單元測試 ---");

    TestCompositePipeline* test = new TestCompositePipeline();
    test.RunTests();
    delete test;

    Print("--- PipelineAdvance_v1 單元測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行整合測試                                                       |
//+------------------------------------------------------------------+
void RunPipelineAdvanceV1IntegrationTests()
{
    Print("\n--- 開始執行 PipelineAdvance_v1 整合測試 ---");

    // 創建整合測試運行器
    SimpleIntegrationTestRunner* runner = new SimpleIntegrationTestRunner();

    // 運行整合測試
    runner.RunIntegrationTests();

    // 清理
    delete runner;

    Print("--- PipelineAdvance_v1 整合測試完成 ---");
}

//+------------------------------------------------------------------+
//| 運行所有測試（簡化版）                                             |
//+------------------------------------------------------------------+
void RunAllPipelineAdvanceV1TestsSimple()
{
    Print("\n📄 開始執行 PipelineAdvance_v1 完整測試套件...");

    // 運行單元測試
    RunPipelineAdvanceV1UnitTests();

    // 運行整合測試
    RunPipelineAdvanceV1IntegrationTests();

    Print("✅ PipelineAdvance_v1 完整測試套件執行完成");
}

//+------------------------------------------------------------------+
//| 快速測試檢查                                                       |
//+------------------------------------------------------------------+
bool QuickPipelineAdvanceV1Check()
{
    Print("⚡ 開始快速 PipelineAdvance_v1 測試檢查...");

    bool unitTestsPassed = true;
    bool integrationTestsPassed = true;

    // 快速單元測試
    TestCompositePipeline* unitTest = new TestCompositePipeline();
    unitTest.RunTests();
    delete unitTest;

    // 快速整合測試
    SimpleIntegrationTestRunner* integrationRunner = new SimpleIntegrationTestRunner();
    integrationRunner.RunIntegrationTests();
    integrationTestsPassed = integrationRunner.AllTestsPassed();
    delete integrationRunner;

    bool allPassed = unitTestsPassed && integrationTestsPassed;

    if(allPassed)
    {
        Print("✅ 快速測試檢查通過");
    }
    else
    {
        Print("❌ 快速測試檢查失敗");
    }

    return allPassed;
}

//+------------------------------------------------------------------+
//| Script program start function                                    |
//+------------------------------------------------------------------+
void OnStart()
{
    // 默認運行所有測試
    RunAllPipelineAdvanceV1Tests();
}

