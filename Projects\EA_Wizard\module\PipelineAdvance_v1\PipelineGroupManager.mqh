//+------------------------------------------------------------------+
//|                                       PipelineGroupManager.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "IPipelineManager.mqh"
#include "PipelineGroup.mqh"
#include "../RegistryAdvance/Vector.mqh"

//+------------------------------------------------------------------+
//| 流水線組管理器類                                                 |
//+------------------------------------------------------------------+
class PipelineGroupManager : public IPipelineManager
{
private:
    string m_name;                          // 管理器名稱
    string m_type;                          // 管理器類型
    Vector<PipelineGroup*> m_groups;        // 流水線組向量
    bool m_owned;                           // 是否擁有流水線組
    bool m_isEnabled;                       // 是否啟用
    bool m_executed;                        // 是否已執行
    int m_maxGroups;                        // 最大流水線組數量

public:
    // 構造函數
    PipelineGroupManager(string name = "PipelineGroupManager", 
                        string type = "PipelineGroupManager",
                        bool owned = false,
                        int maxGroups = 50)
        : m_name(name),
          m_type(type),
          m_groups(owned),
          m_owned(owned),
          m_isEnabled(true),
          m_executed(false),
          m_maxGroups(maxGroups)
    {
    }
    
    // 析構函數
    virtual ~PipelineGroupManager()
    {
        Clear();
    }
    
    // 添加流水線組
    virtual bool AddGroup(PipelineGroup* group) override
    {
        if(group == NULL)
        {
            return false;
        }
        
        // 檢查是否超過最大數量限制
        if(m_groups.size() >= m_maxGroups)
        {
            return false;
        }
        
        // 檢查是否已存在同名組
        if(FindGroupByName(group.GetName()) != NULL)
        {
            return false;
        }
        
        return m_groups.add(group);
    }
    
    // 移除流水線組
    virtual bool RemoveGroup(PipelineGroup* group) override
    {
        if(group == NULL)
        {
            return false;
        }
        
        return m_groups.remove(group);
    }
    
    // 執行所有流水線組
    virtual void ExecuteAll() override
    {
        if(m_executed || !m_isEnabled)
        {
            return;
        }
        
        foreachv(PipelineGroup*, group, m_groups)
        {
            if(group != NULL && group.IsEnabled())
            {
                group.ExecuteAll();
            }
        }
        
        m_executed = true;
    }
    
    // 重置所有流水線組
    virtual void RestoreAll() override
    {
        foreachv(PipelineGroup*, group, m_groups)
        {
            if(group != NULL)
            {
                group.RestoreAll();
            }
        }
        
        m_executed = false;
    }
    
    // 按名稱查找流水線組
    virtual PipelineGroup* FindGroupByName(string name) override
    {
        foreachv(PipelineGroup*, group, m_groups)
        {
            if(group != NULL && group.GetName() == name)
            {
                return group;
            }
        }
        return NULL;
    }
    
    // 獲取流水線組數量
    virtual int GetGroupCount() override
    {
        return m_groups.size();
    }
    
    // 獲取管理器名稱
    virtual string GetName() override
    {
        return m_name;
    }
    
    // 獲取管理器類型
    virtual string GetType() override
    {
        return m_type;
    }
    
    // 檢查是否已執行
    virtual bool IsExecuted() override
    {
        return m_executed;
    }
    
    // 設置啟用狀態
    virtual void SetEnabled(bool enabled) override
    {
        m_isEnabled = enabled;
    }
    
    // 檢查是否啟用
    virtual bool IsEnabled() override
    {
        return m_isEnabled;
    }
    
    // 清理所有流水線組
    virtual void Clear() override
    {
        m_groups.clear();
        m_executed = false;
    }
    
    // 獲取所有流水線組
    virtual int GetAllGroups(PipelineGroup* &groups[]) override
    {
        int size = m_groups.size();
        ArrayResize(groups, size);
        
        for(int i = 0; i < size; i++)
        {
            groups[i] = m_groups.get(i);
        }
        
        return size;
    }
    
    // 獲取最大流水線組數量
    int GetMaxGroups() const
    {
        return m_maxGroups;
    }
    
    // 設置最大流水線組數量
    void SetMaxGroups(int maxGroups)
    {
        m_maxGroups = maxGroups;
    }
};
