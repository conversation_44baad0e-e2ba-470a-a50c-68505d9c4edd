//+------------------------------------------------------------------+
//|                                       PipelineGroupManager.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "PipelineGroup.mqh"

//+------------------------------------------------------------------+
//| 流水線組管理器類                                                 |
//+------------------------------------------------------------------+
class PipelineGroupManager
{
private:
    string m_name;                          // 管理器名稱
    string m_type;                          // 管理器類型
    PipelineGroup* m_group1;                // 第一個流水線組
    PipelineGroup* m_group2;                // 第二個流水線組
    PipelineGroup* m_group3;                // 第三個流水線組
    bool m_owned;                           // 是否擁有流水線組
    bool m_isEnabled;                       // 是否啟用
    bool m_executed;                        // 是否已執行

public:
    // 構造函數
    PipelineGroupManager(string name = "PipelineGroupManager",
                        string type = "PipelineGroupManager",
                        bool owned = false)
        : m_name(name),
          m_type(type),
          m_group1(NULL),
          m_group2(NULL),
          m_group3(NULL),
          m_owned(owned),
          m_isEnabled(true),
          m_executed(false)
    {
    }

    // 析構函數
    virtual ~PipelineGroupManager()
    {
        Clear();
    }

    // 添加流水線組
    bool AddGroup(PipelineGroup* group)
    {
        if(group == NULL)
        {
            return false;
        }

        // 檢查是否已存在同名組
        if(FindGroupByName(group.GetName()) != NULL)
        {
            return false;
        }

        // 找到第一個空位置
        if(m_group1 == NULL)
        {
            m_group1 = group;
            return true;
        }
        else if(m_group2 == NULL)
        {
            m_group2 = group;
            return true;
        }
        else if(m_group3 == NULL)
        {
            m_group3 = group;
            return true;
        }

        // 所有位置都已佔用
        return false;
    }

    // 移除流水線組
    bool RemoveGroup(PipelineGroup* group)
    {
        if(group == NULL)
        {
            return false;
        }

        if(m_group1 == group)
        {
            m_group1 = NULL;
            return true;
        }
        else if(m_group2 == group)
        {
            m_group2 = NULL;
            return true;
        }
        else if(m_group3 == group)
        {
            m_group3 = NULL;
            return true;
        }

        return false;
    }

    // 執行所有流水線組
    void ExecuteAll()
    {
        if(m_executed || !m_isEnabled)
        {
            return;
        }

        if(m_group1 != NULL && m_group1.IsEnabled())
        {
            m_group1.ExecuteAll();
        }

        if(m_group2 != NULL && m_group2.IsEnabled())
        {
            m_group2.ExecuteAll();
        }

        if(m_group3 != NULL && m_group3.IsEnabled())
        {
            m_group3.ExecuteAll();
        }

        m_executed = true;
    }

    // 重置所有流水線組
    void RestoreAll()
    {
        if(m_group1 != NULL)
        {
            m_group1.RestoreAll();
        }

        if(m_group2 != NULL)
        {
            m_group2.RestoreAll();
        }

        if(m_group3 != NULL)
        {
            m_group3.RestoreAll();
        }

        m_executed = false;
    }

    // 按名稱查找流水線組
    PipelineGroup* FindGroupByName(string name)
    {
        if(m_group1 != NULL && m_group1.GetName() == name)
        {
            return m_group1;
        }

        if(m_group2 != NULL && m_group2.GetName() == name)
        {
            return m_group2;
        }

        if(m_group3 != NULL && m_group3.GetName() == name)
        {
            return m_group3;
        }

        return NULL;
    }

    // 獲取流水線組數量
    int GetGroupCount()
    {
        int count = 0;
        if(m_group1 != NULL) count++;
        if(m_group2 != NULL) count++;
        if(m_group3 != NULL) count++;
        return count;
    }

    // 獲取管理器名稱
    string GetName()
    {
        return m_name;
    }

    // 獲取管理器類型
    string GetType()
    {
        return m_type;
    }

    // 檢查是否已執行
    bool IsExecuted()
    {
        return m_executed;
    }

    // 設置啟用狀態
    void SetEnabled(bool enabled)
    {
        m_isEnabled = enabled;
    }

    // 檢查是否啟用
    bool IsEnabled()
    {
        return m_isEnabled;
    }

    // 清理所有流水線組
    void Clear()
    {
        if(m_owned)
        {
            if(m_group1 != NULL) delete m_group1;
            if(m_group2 != NULL) delete m_group2;
            if(m_group3 != NULL) delete m_group3;
        }

        m_group1 = NULL;
        m_group2 = NULL;
        m_group3 = NULL;
        m_executed = false;
    }

    // 獲取所有流水線組
    int GetAllGroups(PipelineGroup* &groups[])
    {
        int count = GetGroupCount();
        ArrayResize(groups, count);

        int index = 0;
        if(m_group1 != NULL) groups[index++] = m_group1;
        if(m_group2 != NULL) groups[index++] = m_group2;
        if(m_group3 != NULL) groups[index++] = m_group3;

        return count;
    }

    // 獲取最大流水線組數量（固定為3）
    int GetMaxGroups() const
    {
        return 3;
    }

    // 獲取指定索引的流水線組
    PipelineGroup* GetGroup(int index)
    {
        if(index == 0) return m_group1;
        if(index == 1) return m_group2;
        if(index == 2) return m_group3;
        return NULL;
    }

    // 檢查是否有空位置
    bool HasEmptySlot()
    {
        return (m_group1 == NULL || m_group2 == NULL || m_group3 == NULL);
    }
};
