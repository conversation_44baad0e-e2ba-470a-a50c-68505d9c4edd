# PipelineAdvance_v1 修改指南

## 📋 修改概述

本次修改對 PipelineAdvance_v1 模組進行了以下重要更新：

### 1. TradingPipeline 增強
- ✅ 添加 `IPipelineManager* m_manager` 成員
- ✅ 添加 `ENUM_TRADING_STAGE m_stage` 成員
- ✅ 修改建構子支援新參數
- ✅ 添加 `GetStage()`, `GetManager()`, `SetManager()` 方法

### 2. 新增 Manager 架構
- ✅ 創建 `IPipelineManager` 介面
- ✅ 實現 `PipelineGroupManager` 類別
- ✅ 支援管理多個 `PipelineGroup`

### 3. CompositePipeline 重構
- ✅ 改為直接繼承 `ITradingPipeline`
- ✅ 重新實現所有介面方法
- ✅ 添加必要的基本成員變數

## 🚀 使用方法

### 基本使用示例

```mql4
// 1. 創建管理器
PipelineGroupManager* manager = new PipelineGroupManager("主管理器");

// 2. 創建具體流水線（帶有階段和管理器）
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(IPipelineManager* manager) 
        : TradingPipeline("數據饋送", "DataFeed", TICK_DATA_FEED, manager) {}
        
protected:
    void Main() override
    {
        Print("執行數據饋送，階段：", EnumToString(GetStage()));
    }
};

// 3. 創建流水線實例
DataFeedPipeline* dataFeed = new DataFeedPipeline(manager);
SignalPipeline* signal = new SignalPipeline(manager);

// 4. 創建複合流水線
CompositePipeline* composite = new CompositePipeline("主流水線");
composite.AddPipeline(dataFeed);
composite.AddPipeline(signal);

// 5. 創建流水線組
PipelineGroup* tickGroup = new PipelineGroup("Tick處理組", 
                                            "處理每個Tick", 
                                            TRADING_TICK);
tickGroup.AddPipeline(composite);

// 6. 添加到管理器
manager.AddGroup(tickGroup);

// 7. 執行所有流水線
manager.ExecuteAll();
```

## 🔧 新增功能詳解

### IPipelineManager 介面

```mql4
interface IPipelineManager
{
    bool AddGroup(PipelineGroup* group);        // 添加流水線組
    bool RemoveGroup(PipelineGroup* group);     // 移除流水線組
    void ExecuteAll();                          // 執行所有組
    void RestoreAll();                          // 重置所有組
    PipelineGroup* FindGroupByName(string name); // 按名稱查找組
    int GetGroupCount();                        // 獲取組數量
    string GetName();                           // 獲取管理器名稱
    // ... 其他方法
};
```

### TradingPipeline 新建構子

```mql4
TradingPipeline(string name = "",
               string type = "TradingPipeline", 
               ENUM_TRADING_STAGE stage = INIT_START,
               IPipelineManager* manager = NULL)
```

### ENUM_TRADING_STAGE 階段

```mql4
enum ENUM_TRADING_STAGE
{
    // 初始化階段
    INIT_START,
    INIT_PARAMETERS,
    INIT_VARIABLES,
    INIT_ENVIRONMENT,
    INIT_INDICATORS,
    INIT_COMPLETE,
    
    // 交易階段
    TICK_DATA_FEED,
    TICK_SIGNAL_ANALYSIS,
    TICK_ORDER_MANAGEMENT,
    TICK_RISK_CONTROL,
    TICK_LOGGING,
    
    // 清理階段
    DEINIT_CLEANUP,
    DEINIT_SAVE_STATE,
    DEINIT_COMPLETE
};
```

## 📊 架構變化

### 修改前
```
ITradingPipeline
    ↑
TradingPipeline
    ↑
CompositePipeline
```

### 修改後
```
ITradingPipeline
    ↑                    ↑
TradingPipeline    CompositePipeline
    ↑
具體流水線實現

IPipelineManager
    ↑
PipelineGroupManager
    ↓ 管理
PipelineGroup
    ↓ 包含
CompositePipeline
```

## 🧪 測試

運行測試文件驗證修改：

```mql4
#include "test/TestModifications.mq4"
```

測試涵蓋：
- ✅ Manager 創建和管理
- ✅ TradingPipeline 新參數
- ✅ CompositePipeline 新繼承關係
- ✅ 完整的執行流程
- ✅ 重置功能

## 🔄 向後兼容性

### 保持兼容的部分
- ✅ ITradingPipeline 介面未變
- ✅ PipelineGroup 基本功能未變
- ✅ CompositePipeline 公共方法未變

### 需要更新的部分
- ⚠️ TradingPipeline 子類需要更新建構子
- ⚠️ 直接使用 CompositePipeline 繼承的代碼需要檢查

## 📝 最佳實踐

1. **使用 Manager 模式**：通過 PipelineGroupManager 統一管理所有流水線組
2. **明確階段定義**：為每個流水線指定合適的 ENUM_TRADING_STAGE
3. **資源管理**：使用 owned 參數正確管理物件生命週期
4. **錯誤處理**：檢查 AddGroup, AddPipeline 等方法的返回值

## 🚨 注意事項

- Manager 指標可以為 NULL，但建議總是提供有效的管理器
- CompositePipeline 現在不再有 Manager 和 Stage，這些由其子流水線提供
- 確保正確的 #include 順序避免編譯錯誤
- 使用前向聲明避免循環依賴
