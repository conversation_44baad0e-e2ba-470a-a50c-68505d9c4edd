//+------------------------------------------------------------------+
//|                                              SimpleTestRunner.mqh |
//|                                                       EA_Wizard |
//|                                                                 |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property strict

#include "../TestFramework.mqh"
#include "../../TradingPipeline.mqh"
#include "../../CompositePipeline.mqh"
#include "../../PipelineGroup.mqh"
#include "MockTradingPipeline.mqh"

//+------------------------------------------------------------------+
//| 簡化版整合測試運行器                                               |
//+------------------------------------------------------------------+
class SimpleIntegrationTestRunner : public TestRunner
{
public:
    // 構造函數
    SimpleIntegrationTestRunner() : TestRunner() {}

    // 運行整合測試
    void RunIntegrationTests()
    {
        Print("🚀 開始執行 PipelineAdvance_v1 整合測試...");

        // 運行基本工作流程測試
        TestBasicWorkflow();

        // 運行錯誤處理測試
        TestErrorHandling();

        // 運行邊界情況測試
        TestEdgeCases();

        // 顯示摘要
        ShowSummary();

        Print("✅ PipelineAdvance_v1 整合測試執行完成");
    }

private:
    // 測試基本工作流程
    void TestBasicWorkflow()
    {
        Print("=== 測試基本工作流程 ===");

        // 創建基本流水線
        MockTradingPipeline* dataFeed = MockTradingPipelineFactory::CreateDataFeedPipeline();
        MockTradingPipeline* signal = MockTradingPipelineFactory::CreateSignalPipeline();
        MockTradingPipeline* order = MockTradingPipelineFactory::CreateOrderPipeline();

        // 創建複合流水線
        CompositePipeline* mainPipeline = new CompositePipeline("主工作流程");
        mainPipeline.AddPipeline(dataFeed);
        mainPipeline.AddPipeline(signal);
        mainPipeline.AddPipeline(order);

        // 創建流水線組
        PipelineGroup* tickGroup = new PipelineGroup("Tick處理組", "處理每個Tick的流水線組", TRADING_TICK);
        tickGroup.AddPipeline(mainPipeline);

        // 執行工作流程
        tickGroup.ExecuteAll();

        // 驗證結果
        RecordResult(Assert::AssertTrue("基本工作流程_組執行成功", tickGroup.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_主流水線執行", mainPipeline.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_數據饋送執行", dataFeed.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_信號分析執行", signal.IsExecuted()));
        RecordResult(Assert::AssertTrue("基本工作流程_訂單處理執行", order.IsExecuted()));
        RecordResult(Assert::AssertEquals("基本工作流程_流水線數量", 3, mainPipeline.GetPipelineCount()));

        // 清理
        delete tickGroup;
        delete mainPipeline;
        delete dataFeed;
        delete signal;
        delete order;
    }

    // 測試錯誤處理
    void TestErrorHandling()
    {
        Print("=== 測試錯誤處理 ===");

        // 創建包含失敗流水線的複合流水線
        CompositePipeline* errorPipeline = new CompositePipeline("錯誤處理測試");

        MockTradingPipeline* success1 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功1");
        MockTradingPipeline* failed = MockTradingPipelineFactory::CreateFailedPipeline("失敗");
        MockTradingPipeline* success2 = MockTradingPipelineFactory::CreateSuccessfulPipeline("成功2");

        errorPipeline.AddPipeline(success1);
        errorPipeline.AddPipeline(failed);
        errorPipeline.AddPipeline(success2);

        // 執行包含錯誤的流水線
        errorPipeline.Execute();

        // 驗證錯誤處理
        RecordResult(Assert::AssertTrue("錯誤處理_複合流水線執行", errorPipeline.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_成功1執行", success1.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_失敗流水線執行", failed.IsExecuted()));
        RecordResult(Assert::AssertTrue("錯誤處理_成功2執行", success2.IsExecuted()));

        // 清理
        delete errorPipeline;
        delete success1;
        delete failed;
        delete success2;
    }

    // 測試邊界情況
    void TestEdgeCases()
    {
        Print("=== 測試邊界情況 ===");

        // 測試空流水線組
        PipelineGroup* emptyGroup = new PipelineGroup("空組", "沒有流水線的組", TRADING_TICK);
        emptyGroup.ExecuteAll();
        RecordResult(Assert::AssertTrue("邊界情況_空組執行", emptyGroup.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_空組流水線數量", 0, emptyGroup.GetPipelineCount()));

        // 測試空複合流水線
        CompositePipeline* emptyComposite = new CompositePipeline("空複合");
        emptyComposite.Execute();
        RecordResult(Assert::AssertTrue("邊界情況_空複合執行", emptyComposite.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_空複合子數量", 0, emptyComposite.GetPipelineCount()));

        // 測試單個流水線的複合
        CompositePipeline* singleComposite = new CompositePipeline("單個複合");
        MockTradingPipeline* singlePipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline("單獨");
        singleComposite.AddPipeline(singlePipeline);
        singleComposite.Execute();

        RecordResult(Assert::AssertTrue("邊界情況_單個複合執行", singleComposite.IsExecuted()));
        RecordResult(Assert::AssertTrue("邊界情況_單個子流水線執行", singlePipeline.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_單個複合子數量", 1, singleComposite.GetPipelineCount()));

        // 測試重複執行防護
        singleComposite.Execute(); // 再次執行
        RecordResult(Assert::AssertTrue("邊界情況_重複執行防護", singleComposite.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_重複執行後執行次數", 1, singlePipeline.GetExecutionCount()));

        // 測試重置後重新執行
        singleComposite.Restore();
        singlePipeline.Restore();
        singlePipeline.ResetExecutionCount();

        singleComposite.Execute();
        RecordResult(Assert::AssertTrue("邊界情況_重置後重新執行", singleComposite.IsExecuted()));
        RecordResult(Assert::AssertEquals("邊界情況_重置後執行次數", 1, singlePipeline.GetExecutionCount()));

        // 清理
        delete emptyGroup;
        delete emptyComposite;
        delete singleComposite;
        delete singlePipeline;
    }

    // 測試大規模場景
    void TestLargeScaleScenario()
    {
        Print("=== 測試大規模場景 ===");

        // 創建大規模流水線組
        PipelineGroup* largeGroup = new PipelineGroup("大規模組", "包含大量流水線的組", TRADING_TICK);

        // 創建多個複合流水線
        const int COMPOSITE_COUNT = 3;
        const int PIPELINES_PER_COMPOSITE = 5;

        CompositePipeline* composites[];
        ArrayResize(composites, COMPOSITE_COUNT);

        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            string compositeName = "大規模複合_" + IntegerToString(i + 1);
            composites[i] = new CompositePipeline(compositeName);

            // 每個複合流水線添加多個子流水線
            for(int j = 0; j < PIPELINES_PER_COMPOSITE; j++)
            {
                string pipelineName = "大規模流水線_" + IntegerToString(i + 1) + "_" + IntegerToString(j + 1);
                MockTradingPipeline* pipeline = MockTradingPipelineFactory::CreateSuccessfulPipeline(pipelineName, 5);
                composites[i].AddPipeline(pipeline);
            }

            largeGroup.AddPipeline(composites[i]);
        }

        // 執行大規模場景
        datetime startTime = TimeCurrent();
        largeGroup.ExecuteAll();
        datetime endTime = TimeCurrent();

        // 驗證大規模執行
        RecordResult(Assert::AssertTrue("大規模場景_組執行完成", largeGroup.IsExecuted()));
        RecordResult(Assert::AssertEquals("大規模場景_複合流水線數量", COMPOSITE_COUNT, largeGroup.GetPipelineCount()));

        // 驗證每個複合流水線
        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            string testName = "大規模場景_複合" + IntegerToString(i + 1) + "執行";
            RecordResult(Assert::AssertTrue(testName, composites[i].IsExecuted()));

            string countTestName = "大規模場景_複合" + IntegerToString(i + 1) + "子數量";
            RecordResult(Assert::AssertEquals(countTestName, PIPELINES_PER_COMPOSITE, composites[i].GetPipelineCount()));
        }

        Print("大規模場景執行時間: " + IntegerToString(endTime - startTime) + " 秒");

        // 清理
        delete largeGroup;
        for(int i = 0; i < COMPOSITE_COUNT; i++)
        {
            delete composites[i];
        }
    }
};
