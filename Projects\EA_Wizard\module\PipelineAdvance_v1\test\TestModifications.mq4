//+------------------------------------------------------------------+
//|                                            TestModifications.mq4 |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../TradingPipeline.mqh"
#include "../CompositePipeline.mqh"
#include "../PipelineGroupManager.mqh"
#include "../PipelineGroup.mqh"

//+------------------------------------------------------------------+
//| 測試用的具體流水線實現                                           |
//+------------------------------------------------------------------+
class TestTradingPipeline : public TradingPipeline
{
public:
    TestTradingPipeline(string name,
                       ENUM_TRADING_STAGE stage = TICK_DATA_FEED,
                       PipelineGroupManager* manager = NULL)
        : TradingPipeline(name, "TestTradingPipeline", stage, manager)
    {
    }

protected:
    virtual void Main() override
    {
        Print("執行測試流水線: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

//+------------------------------------------------------------------+
//| Script program start function                                     |
//+------------------------------------------------------------------+
void OnStart()
{
    Print("=== PipelineAdvance_v1 修改測試開始 ===");

    // 測試 1: 創建 PipelineGroupManager（現在最多支援3個組）
    Print("\n--- 測試 1: 創建 PipelineGroupManager ---");
    PipelineGroupManager* manager = new PipelineGroupManager("主管理器");
    Print("管理器名稱: ", manager.GetName());
    Print("管理器類型: ", manager.GetType());
    Print("初始組數量: ", manager.GetGroupCount());
    Print("最大組數量: ", manager.GetMaxGroups());
    Print("是否有空位置: ", manager.HasEmptySlot());

    // 測試 2: 創建帶有 Manager 和 Stage 的 TradingPipeline
    Print("\n--- 測試 2: 創建 TradingPipeline ---");
    TestTradingPipeline* pipeline1 = new TestTradingPipeline("數據饋送", TICK_DATA_FEED, manager);
    TestTradingPipeline* pipeline2 = new TestTradingPipeline("信號分析", TICK_SIGNAL_ANALYSIS, manager);

    Print("流水線1 - 名稱: ", pipeline1.GetName(), ", 階段: ", EnumToString(pipeline1.GetStage()));
    Print("流水線2 - 名稱: ", pipeline2.GetName(), ", 階段: ", EnumToString(pipeline2.GetStage()));

    // 測試 3: 創建 CompositePipeline (現在直接繼承 ITradingPipeline)
    Print("\n--- 測試 3: 創建 CompositePipeline ---");
    CompositePipeline* composite = new CompositePipeline("複合流水線");
    Print("複合流水線名稱: ", composite.GetName());
    Print("複合流水線類型: ", composite.GetType());
    Print("初始執行狀態: ", composite.IsExecuted());

    // 測試 4: 添加子流水線到 CompositePipeline
    Print("\n--- 測試 4: 添加子流水線 ---");
    bool added1 = composite.AddPipeline(pipeline1);
    bool added2 = composite.AddPipeline(pipeline2);
    Print("添加流水線1結果: ", added1);
    Print("添加流水線2結果: ", added2);
    Print("複合流水線子流水線數量: ", composite.GetPipelineCount());

    // 測試 5: 創建 PipelineGroup 並添加到 Manager
    Print("\n--- 測試 5: 創建 PipelineGroup ---");
    PipelineGroup* group = new PipelineGroup("Tick處理組", "處理每個Tick的流水線", TRADING_TICK);
    bool groupAdded = group.AddPipeline(composite);
    Print("添加複合流水線到組結果: ", groupAdded);
    Print("組中流水線數量: ", group.GetPipelineCount());

    // 測試 6: 添加組到 Manager
    Print("\n--- 測試 6: 添加組到 Manager ---");
    bool managerAdded = manager.AddGroup(group);
    Print("添加組到管理器結果: ", managerAdded);
    Print("管理器中組數量: ", manager.GetGroupCount());

    // 測試 7: 執行整個流水線
    Print("\n--- 測試 7: 執行流水線 ---");
    Print("執行前狀態:");
    Print("  - 複合流水線已執行: ", composite.IsExecuted());
    Print("  - 組已執行: ", group.IsExecuted());
    Print("  - 管理器已執行: ", manager.IsExecuted());

    manager.ExecuteAll();

    Print("執行後狀態:");
    Print("  - 複合流水線已執行: ", composite.IsExecuted());
    Print("  - 組已執行: ", group.IsExecuted());
    Print("  - 管理器已執行: ", manager.IsExecuted());

    // 測試 8: 重置流水線
    Print("\n--- 測試 8: 重置流水線 ---");
    manager.RestoreAll();
    Print("重置後狀態:");
    Print("  - 複合流水線已執行: ", composite.IsExecuted());
    Print("  - 組已執行: ", group.IsExecuted());
    Print("  - 管理器已執行: ", manager.IsExecuted());

    // 測試 9: 測試3個組的限制
    Print("\n--- 測試 9: 測試3個組的限制 ---");
    PipelineGroup* group2 = new PipelineGroup("第二組", "測試組2", TRADING_TICK);
    PipelineGroup* group3 = new PipelineGroup("第三組", "測試組3", TRADING_TICK);
    PipelineGroup* group4 = new PipelineGroup("第四組", "測試組4", TRADING_TICK);

    bool added2 = manager.AddGroup(group2);
    bool added3 = manager.AddGroup(group3);
    bool added4 = manager.AddGroup(group4);  // 這應該失敗

    Print("添加第二組結果: ", added2);
    Print("添加第三組結果: ", added3);
    Print("添加第四組結果: ", added4, " (應該失敗)");
    Print("最終組數量: ", manager.GetGroupCount());
    Print("是否還有空位置: ", manager.HasEmptySlot());

    // 測試按索引獲取組
    Print("\n--- 測試按索引獲取組 ---");
    for(int i = 0; i < 3; i++)
    {
        PipelineGroup* g = manager.GetGroup(i);
        if(g != NULL)
        {
            Print("組[", i, "]: ", g.GetName());
        }
        else
        {
            Print("組[", i, "]: NULL");
        }
    }

    // 清理資源
    Print("\n--- 清理資源 ---");
    delete group4;  // 這個沒有被添加到管理器，需要手動刪除
    delete manager;  // 這會自動清理所有相關資源

    Print("\n=== PipelineAdvance_v1 修改測試完成 ===");
}
//+------------------------------------------------------------------+
