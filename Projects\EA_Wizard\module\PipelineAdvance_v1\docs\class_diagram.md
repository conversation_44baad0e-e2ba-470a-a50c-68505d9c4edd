# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義了基本的流水線操作
- **TradingPipeline**: 抽象基類，實現了 ITradingPipeline 介面的基本功能
- **CompositePipeline**: 組合類，實現了組合模式，可以包含多個子流水線
- **PipelineGroup**: 流水線組，管理多個複合流水線
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 類別圖

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
    }

    %% 結果類
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        -datetime m_timestamp
        -ENUM_ERROR_LEVEL m_errorLevel
        +PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel)
        +~PipelineResult()
        +IsSuccess() bool
        +GetMessage() string
        +GetSource() string
        +GetTimestamp() datetime
        +GetErrorLevel() ENUM_ERROR_LEVEL
        +ToString() string
    }

    %% 抽象基類
    class TradingPipeline {
        <<abstract>>
        #string m_name
        #string m_type
        #bool m_executed
        +TradingPipeline(string name, string type)
        +~TradingPipeline()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        #Main() void*
    }

    %% 複合流水線
    class CompositePipeline {
        -Vector~ITradingPipeline*~ m_pipelines
        -bool m_owned
        -int m_maxPipelines
        -PipelineResult* m_last_result
        +CompositePipeline(string name, bool owned, int maxPipelines)
        +~CompositePipeline()
        +AddPipeline(ITradingPipeline* pipeline) bool
        +RemovePipeline(ITradingPipeline* pipeline) bool
        +RemovePipelineByName(string name) bool
        +Clear() void
        +GetPipelineCount() int
        +GetMaxPipelines() int
        +GetPipeline(int index, ITradingPipeline* parent) ITradingPipeline*
        +FindByName(string name, ITradingPipeline* parent) ITradingPipeline*
        +Restore() void
        #Main() void
    }

    %% 流水線組
    class PipelineGroup {
        -string m_name
        -string m_type
        -string m_description
        -Vector~CompositePipeline*~ m_pipelines
        -bool m_owned
        -bool m_isEnabled
        -bool m_executed
        -ENUM_TRADING_EVENT m_eventType
        -PipelineResult* m_last_result
        +PipelineGroup(string name, string description, ENUM_TRADING_EVENT eventType, bool owned, string type)
        +~PipelineGroup()
        +AddPipeline(CompositePipeline* pipeline) bool
        +RemovePipeline(CompositePipeline* pipeline) bool
        +FindPipelineByName(string name, CompositePipeline* parent) CompositePipeline*
        +ExecuteAll() void
        +RestoreAll() void
        +Clear() void
        +GetPipelineCount() int
        +GetName() string
        +GetType() string
        +GetDescription() string
        +IsExecuted() bool
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetEventType() ENUM_TRADING_EVENT
        +GetStatusInfo() string
        +GetPipeline(int index, CompositePipeline* parent) CompositePipeline*
        +GetPipelineNames(string &names[]) void
        +ContainsPipeline(CompositePipeline* pipeline) bool
        +ContainsPipelineByName(string name) bool
        +GetLastResult() PipelineResult*
    }

    %% 簡單實現示例
    class SimpleTradingPipeline {
        +SimpleTradingPipeline()
    }

    %% 枚舉類型
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        INIT_PARAMETERS
        INIT_VARIABLES
        INIT_ENVIRONMENT
        INIT_INDICATORS
        INIT_COMPLETE
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        TICK_ORDER_MANAGEMENT
        TICK_RISK_CONTROL
        TICK_LOGGING
        DEINIT_CLEANUP
        DEINIT_SAVE_STATE
        DEINIT_COMPLETE
    }

    class ENUM_PIPELINE_STATUS {
        <<enumeration>>
        STATUS_PENDING
        STATUS_RUNNING
        STATUS_COMPLETED
        STATUS_FAILED
        STATUS_CANCELLED
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class ENUM_EXECUTION_MODE {
        <<enumeration>>
        EXECUTION_SEQUENTIAL
        EXECUTION_PARALLEL
        EXECUTION_CONDITIONAL
    }

    %% 工具類
    class TradingEventUtils {
        <<utility>>
        +EventToString(ENUM_TRADING_EVENT event) string$
        +StageToString(ENUM_TRADING_STAGE stage) string$
        +StatusToString(ENUM_PIPELINE_STATUS status) string$
        +IsValidEvent(ENUM_TRADING_EVENT event) bool$
        +IsStageOfEvent(ENUM_TRADING_STAGE stage, ENUM_TRADING_EVENT event) bool$
        +GetEventStages(ENUM_TRADING_EVENT event, ENUM_TRADING_STAGE &stages[]) int$
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline : implements
    TradingPipeline <|-- CompositePipeline : extends
    TradingPipeline <|-- SimpleTradingPipeline : extends
    CompositePipeline o-- ITradingPipeline : contains
    PipelineGroup o-- CompositePipeline : contains
    CompositePipeline --> PipelineResult : uses
    PipelineGroup --> PipelineResult : uses
    PipelineGroup --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_STAGE : uses
    TradingEventUtils --> ENUM_PIPELINE_STATUS : uses
```

## 設計模式

### 1. 組合模式 (Composite Pattern)

- **CompositePipeline** 實現了組合模式，可以包含多個子流水線
- 客戶端可以統一處理單個流水線和複合流水線

### 2. 模板方法模式 (Template Method Pattern)

- **TradingPipeline** 定義了執行流程的骨架
- 子類實現具體的 `Main()` 方法

### 3. 策略模式 (Strategy Pattern)

- 通過不同的 **ENUM_EXECUTION_MODE** 支持不同的執行策略

## 核心特性

### 1. 簡化的架構

- 移除了複雜的裝飾者模式
- 減少了繼承層次
- 專注於核心功能

### 2. 類型安全

- 使用強類型的枚舉定義
- 明確的介面契約

### 3. 錯誤處理

- 統一的錯誤級別定義
- 詳細的執行結果記錄

### 4. 靈活的組織

- 支持流水線的分組管理
- 支持動態添加和移除

## 使用流程

1. **創建流水線**：實現 `ITradingPipeline` 介面或繼承 `TradingPipeline`
2. **組合流水線**：使用 `CompositePipeline` 組合多個子流水線
3. **分組管理**：使用 `PipelineGroup` 管理相關的複合流水線
4. **執行處理**：調用相應的執行方法
5. **結果檢查**：通過 `PipelineResult` 檢查執行結果

## 常量定義

```mql4
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string COMPOSITE_PIPELINE_TYPE = "CompositePipeline";
const string LOGGING_PIPELINE_TYPE = "LoggingPipeline";
const string ERROR_HANDLING_PIPELINE_TYPE = "ErrorHandlingPipeline";

const int DEFAULT_MAX_PIPELINES = 100;
const int DEFAULT_MAX_RETRIES = 3;
const int DEFAULT_TIMEOUT_MS = 5000;
```

## 使用示例

```mql4
// 創建具體的交易流水線
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(string name) : TradingPipeline(name, "DataFeed") {}

protected:
    void Main() override
    {
        // 實現數據饋送邏輯
        Print("執行數據饋送: ", GetName());
    }
};

class SignalPipeline : public TradingPipeline
{
public:
    SignalPipeline(string name) : TradingPipeline(name, "Signal") {}

protected:
    void Main() override
    {
        // 實現信號分析邏輯
        Print("執行信號分析: ", GetName());
    }
};

// 使用示例
void OnStart()
{
    // 創建基本流水線
    DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據");
    SignalPipeline* signal = new SignalPipeline("交易信號");

    // 創建複合流水線
    CompositePipeline* mainPipeline = new CompositePipeline("主流水線");
    mainPipeline.AddPipeline(dataFeed);
    mainPipeline.AddPipeline(signal);

    // 創建流水線組
    PipelineGroup* tickGroup = new PipelineGroup("Tick處理組",
                                                 "處理每個Tick的流水線組",
                                                 TRADING_TICK);
    tickGroup.AddPipeline(mainPipeline);

    // 執行流水線組
    tickGroup.ExecuteAll();

    // 檢查執行結果
    Print("流水線組執行狀態: ", tickGroup.IsExecuted());
    Print("流水線數量: ", mainPipeline.GetPipelineCount());

    // 清理資源
    delete tickGroup;
    delete mainPipeline;
    delete dataFeed;
    delete signal;
}
```

## 優勢

1. **簡潔性**：相比原始的 PipelineAdvance 模組，結構更加簡潔
2. **可維護性**：清晰的類別關係和職責分離
3. **可擴展性**：支持新的流水線類型和執行模式
4. **可測試性**：每個組件都可以獨立測試
5. **性能**：減少了不必要的抽象層，提高執行效率
