# PipelineAdvance_v1 模組類別圖

PipelineAdvance_v1 模組是一個簡化的流水線處理架構，實現了組合模式和模板方法模式，專注於交易流水線的管理和執行。

## 核心概念

- **ITradingPipeline**: 交易流水線介面，定義了基本的流水線操作
- **TradingPipeline**: 抽象基類，實現了 ITradingPipeline 介面的基本功能，包含 Manager 和 Stage
- **CompositePipeline**: 組合類，直接實現 ITradingPipeline 介面，可以包含多個子流水線
- **PipelineGroupManager**: 簡化的管理器類別，負責管理最多 3 個 PipelineGroup
- **PipelineGroup**: 流水線組，管理多個複合流水線
- **PipelineResult**: 結果類，用於存儲流水線執行結果

## 類別圖

```mermaid
classDiagram
    %% 核心介面
    class ITradingPipeline {
        <<interface>>
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
    }



    %% 結果類
    class PipelineResult {
        -bool m_success
        -string m_message
        -string m_source
        -datetime m_timestamp
        -ENUM_ERROR_LEVEL m_errorLevel
        +PipelineResult(bool success, string message, string source, ENUM_ERROR_LEVEL errorLevel)
        +~PipelineResult()
        +IsSuccess() bool
        +GetMessage() string
        +GetSource() string
        +GetTimestamp() datetime
        +GetErrorLevel() ENUM_ERROR_LEVEL
        +ToString() string
    }

    %% 管理器類別（簡化版）
    class PipelineGroupManager {
        -string m_name
        -string m_type
        -PipelineGroup* m_group1
        -PipelineGroup* m_group2
        -PipelineGroup* m_group3
        -bool m_owned
        -bool m_isEnabled
        -bool m_executed
        +PipelineGroupManager(string name, string type, bool owned)
        +~PipelineGroupManager()
        +AddGroup(PipelineGroup* group) bool
        +RemoveGroup(PipelineGroup* group) bool
        +ExecuteAll() void
        +RestoreAll() void
        +FindGroupByName(string name) PipelineGroup*
        +GetGroupCount() int
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +Clear() void
        +GetAllGroups(PipelineGroup* &groups[]) int
        +GetMaxGroups() int
        +GetGroup(int index) PipelineGroup*
        +HasEmptySlot() bool
    }

    %% 抽象基類
    class TradingPipeline {
        <<abstract>>
        #string m_name
        #string m_type
        #bool m_executed
        #ENUM_TRADING_STAGE m_stage
        #PipelineGroupManager* m_manager
        +TradingPipeline(string name, string type, ENUM_TRADING_STAGE stage, PipelineGroupManager* manager)
        +~TradingPipeline()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +GetStage() ENUM_TRADING_STAGE
        +GetManager() PipelineGroupManager*
        +SetManager(PipelineGroupManager* manager) void
        #Main() void*
    }

    %% 複合流水線
    class CompositePipeline {
        -string m_name
        -string m_type
        -bool m_executed
        -Vector~ITradingPipeline*~ m_pipelines
        -bool m_owned
        -int m_maxPipelines
        -PipelineResult* m_last_result
        +CompositePipeline(string name, string type, bool owned, int maxPipelines)
        +~CompositePipeline()
        +Execute() void
        +GetName() string
        +GetType() string
        +IsExecuted() bool
        +Restore() void
        +AddPipeline(ITradingPipeline* pipeline) bool
        +RemovePipeline(ITradingPipeline* pipeline) bool
        +RemovePipelineByName(string name) bool
        +Clear() void
        +GetPipelineCount() int
        +GetMaxPipelines() int
        +GetPipeline(int index, ITradingPipeline* parent) ITradingPipeline*
        +FindByName(string name, ITradingPipeline* parent) ITradingPipeline*
        +GetResult() PipelineResult*
    }

    %% 流水線組
    class PipelineGroup {
        -string m_name
        -string m_type
        -string m_description
        -Vector~CompositePipeline*~ m_pipelines
        -bool m_owned
        -bool m_isEnabled
        -bool m_executed
        -ENUM_TRADING_EVENT m_eventType
        -PipelineResult* m_last_result
        +PipelineGroup(string name, string description, ENUM_TRADING_EVENT eventType, bool owned, string type)
        +~PipelineGroup()
        +AddPipeline(CompositePipeline* pipeline) bool
        +RemovePipeline(CompositePipeline* pipeline) bool
        +FindPipelineByName(string name, CompositePipeline* parent) CompositePipeline*
        +ExecuteAll() void
        +RestoreAll() void
        +Clear() void
        +GetPipelineCount() int
        +GetName() string
        +GetType() string
        +GetDescription() string
        +IsExecuted() bool
        +SetEnabled(bool enabled) void
        +IsEnabled() bool
        +GetEventType() ENUM_TRADING_EVENT
        +GetStatusInfo() string
        +GetPipeline(int index, CompositePipeline* parent) CompositePipeline*
        +GetPipelineNames(string &names[]) void
        +ContainsPipeline(CompositePipeline* pipeline) bool
        +ContainsPipelineByName(string name) bool
        +GetLastResult() PipelineResult*
    }

    %% 簡單實現示例
    class SimpleTradingPipeline {
        +SimpleTradingPipeline()
    }

    %% 枚舉類型
    class ENUM_TRADING_EVENT {
        <<enumeration>>
        TRADING_INIT
        TRADING_TICK
        TRADING_DEINIT
    }

    class ENUM_TRADING_STAGE {
        <<enumeration>>
        INIT_START
        INIT_PARAMETERS
        INIT_VARIABLES
        INIT_ENVIRONMENT
        INIT_INDICATORS
        INIT_COMPLETE
        TICK_DATA_FEED
        TICK_SIGNAL_ANALYSIS
        TICK_ORDER_MANAGEMENT
        TICK_RISK_CONTROL
        TICK_LOGGING
        DEINIT_CLEANUP
        DEINIT_SAVE_STATE
        DEINIT_COMPLETE
    }

    class ENUM_PIPELINE_STATUS {
        <<enumeration>>
        STATUS_PENDING
        STATUS_RUNNING
        STATUS_COMPLETED
        STATUS_FAILED
        STATUS_CANCELLED
    }

    class ENUM_ERROR_LEVEL {
        <<enumeration>>
        ERROR_LEVEL_INFO
        ERROR_LEVEL_WARNING
        ERROR_LEVEL_ERROR
        ERROR_LEVEL_CRITICAL
    }

    class ENUM_EXECUTION_MODE {
        <<enumeration>>
        EXECUTION_SEQUENTIAL
        EXECUTION_PARALLEL
        EXECUTION_CONDITIONAL
    }

    %% 工具類
    class TradingEventUtils {
        <<utility>>
        +EventToString(ENUM_TRADING_EVENT event) string$
        +StageToString(ENUM_TRADING_STAGE stage) string$
        +StatusToString(ENUM_PIPELINE_STATUS status) string$
        +IsValidEvent(ENUM_TRADING_EVENT event) bool$
        +IsStageOfEvent(ENUM_TRADING_STAGE stage, ENUM_TRADING_EVENT event) bool$
        +GetEventStages(ENUM_TRADING_EVENT event, ENUM_TRADING_STAGE &stages[]) int$
    }

    %% 關係
    ITradingPipeline <|.. TradingPipeline : implements
    ITradingPipeline <|.. CompositePipeline : implements
    TradingPipeline <|-- SimpleTradingPipeline : extends
    TradingPipeline --> PipelineGroupManager : uses
    TradingPipeline --> ENUM_TRADING_STAGE : uses
    CompositePipeline o-- ITradingPipeline : contains
    PipelineGroup o-- CompositePipeline : contains
    PipelineGroupManager o-- PipelineGroup : manages (max 3)
    CompositePipeline --> PipelineResult : uses
    PipelineGroup --> PipelineResult : uses
    PipelineGroup --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_EVENT : uses
    TradingEventUtils --> ENUM_TRADING_STAGE : uses
    TradingEventUtils --> ENUM_PIPELINE_STATUS : uses
```

## 設計模式

### 1. 組合模式 (Composite Pattern)

- **CompositePipeline** 實現了組合模式，可以包含多個子流水線
- 客戶端可以統一處理單個流水線和複合流水線
- **CompositePipeline** 現在直接實現 **ITradingPipeline** 介面

### 2. 模板方法模式 (Template Method Pattern)

- **TradingPipeline** 定義了執行流程的骨架
- 子類實現具體的 `Main()` 方法

### 3. 簡化管理者模式 (Simplified Manager Pattern)

- **PipelineGroupManager** 直接實現管理邏輯（無介面）
- 支持統一管理最多 3 個 **PipelineGroup**
- 使用固定的 3 個成員變數而非動態容器

### 4. 策略模式 (Strategy Pattern)

- 通過不同的 **ENUM_EXECUTION_MODE** 支持不同的執行策略
- 通過 **ENUM_TRADING_STAGE** 支持不同的交易階段

## 核心特性

### 1. 簡化的架構

- 移除了複雜的裝飾者模式
- **CompositePipeline** 直接實現 **ITradingPipeline** 介面
- 專注於核心功能

### 2. 簡化管理器架構

- **PipelineGroupManager** 直接實現管理邏輯（移除介面層）
- 支持最多 3 個 PipelineGroup 的管理
- 使用固定成員變數提高性能

### 3. 階段化處理

- **ENUM_TRADING_STAGE** 定義明確的交易階段
- **TradingPipeline** 包含階段信息和管理器引用
- 支持階段化的流水線執行

### 4. 類型安全

- 使用強類型的枚舉定義
- 明確的介面契約

### 5. 錯誤處理

- 統一的錯誤級別定義
- 詳細的執行結果記錄

### 6. 靈活的組織

- 支持流水線的分組管理
- 支持動態添加和移除
- 多層次的管理架構

## 使用流程

1. **創建管理器**：創建 `PipelineGroupManager` 實例
2. **創建流水線**：實現 `ITradingPipeline` 介面或繼承 `TradingPipeline`（包含階段和管理器）
3. **組合流水線**：使用 `CompositePipeline` 組合多個子流水線
4. **分組管理**：使用 `PipelineGroup` 管理相關的複合流水線
5. **註冊到管理器**：將 `PipelineGroup` 添加到 `PipelineGroupManager`
6. **執行處理**：通過管理器統一執行所有流水線
7. **結果檢查**：通過 `PipelineResult` 檢查執行結果

## 常量定義

```mql4
const string TRADING_PIPELINE_TYPE = "TradingPipeline";
const string COMPOSITE_PIPELINE_TYPE = "CompositePipeline";
const string LOGGING_PIPELINE_TYPE = "LoggingPipeline";
const string ERROR_HANDLING_PIPELINE_TYPE = "ErrorHandlingPipeline";

const int DEFAULT_MAX_PIPELINES = 100;
const int DEFAULT_MAX_RETRIES = 3;
const int DEFAULT_TIMEOUT_MS = 5000;
```

## 使用示例

```mql4
// 創建具體的交易流水線
class DataFeedPipeline : public TradingPipeline
{
public:
    DataFeedPipeline(string name, PipelineGroupManager* manager)
        : TradingPipeline(name, "DataFeed", TICK_DATA_FEED, manager) {}

protected:
    void Main() override
    {
        // 實現數據饋送邏輯
        Print("執行數據饋送: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

class SignalPipeline : public TradingPipeline
{
public:
    SignalPipeline(string name, PipelineGroupManager* manager)
        : TradingPipeline(name, "Signal", TICK_SIGNAL_ANALYSIS, manager) {}

protected:
    void Main() override
    {
        // 實現信號分析邏輯
        Print("執行信號分析: ", GetName(), ", 階段: ", EnumToString(GetStage()));
    }
};

// 使用示例
void OnStart()
{
    // 1. 創建管理器
    PipelineGroupManager* manager = new PipelineGroupManager("主管理器");

    // 2. 創建基本流水線（包含階段和管理器）
    DataFeedPipeline* dataFeed = new DataFeedPipeline("市場數據", manager);
    SignalPipeline* signal = new SignalPipeline("交易信號", manager);

    // 3. 創建複合流水線（直接實現 ITradingPipeline）
    CompositePipeline* mainPipeline = new CompositePipeline("主流水線");
    mainPipeline.AddPipeline(dataFeed);
    mainPipeline.AddPipeline(signal);

    // 4. 創建流水線組
    PipelineGroup* tickGroup = new PipelineGroup("Tick處理組",
                                                 "處理每個Tick的流水線組",
                                                 TRADING_TICK);
    tickGroup.AddPipeline(mainPipeline);

    // 5. 添加組到管理器
    manager.AddGroup(tickGroup);

    // 6. 通過管理器執行所有流水線
    manager.ExecuteAll();

    // 7. 檢查執行結果
    Print("管理器執行狀態: ", manager.IsExecuted());
    Print("管理器中組數量: ", manager.GetGroupCount());
    Print("流水線組執行狀態: ", tickGroup.IsExecuted());
    Print("複合流水線數量: ", mainPipeline.GetPipelineCount());

    // 8. 清理資源
    delete manager;  // 會自動清理所有相關資源
}
```

## 優勢

1. **簡潔性**：相比原始的 PipelineAdvance 模組，結構更加簡潔
2. **管理性**：通過 Manager 模式實現統一的流水線管理
3. **階段化**：明確的交易階段定義，支持階段化處理
4. **可維護性**：清晰的類別關係和職責分離
5. **可擴展性**：支持新的流水線類型和執行模式
6. **可測試性**：每個組件都可以獨立測試
7. **性能**：減少了不必要的抽象層，提高執行效率
8. **靈活性**：CompositePipeline 直接實現介面，提供更大的靈活性

## 🔄 架構變化總結

### 修改前

```
ITradingPipeline
    ↑
TradingPipeline
    ↑
CompositePipeline
```

### 修改後

```
ITradingPipeline
    ↑                    ↑
TradingPipeline    CompositePipeline
    ↑
具體流水線實現

PipelineGroupManager (簡化類別)
    ↓ 管理 (固定3個成員)
PipelineGroup
    ↓ 包含
CompositePipeline
```

### 主要改進

1. **✅ TradingPipeline 增強**：添加 Manager 和 Stage 支持
2. **✅ CompositePipeline 重構**：直接實現 ITradingPipeline 介面
3. **✅ Manager 架構**：簡化的管理器模式（移除介面，固定 3 個組）
4. **✅ 階段化處理**：支持明確的交易階段定義
