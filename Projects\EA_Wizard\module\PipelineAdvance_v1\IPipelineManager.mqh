//+------------------------------------------------------------------+
//|                                           IPipelineManager.mqh |
//|                                            PipelineAdvance_v1   |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

// 前向聲明
class PipelineGroup;

//+------------------------------------------------------------------+
//| 流水線管理器介面                                                 |
//+------------------------------------------------------------------+
interface IPipelineManager
{
    // 添加流水線組
    bool AddGroup(PipelineGroup* group);
    
    // 移除流水線組
    bool RemoveGroup(PipelineGroup* group);
    
    // 執行所有流水線組
    void ExecuteAll();
    
    // 重置所有流水線組
    void RestoreAll();
    
    // 按名稱查找流水線組
    PipelineGroup* FindGroupByName(string name);
    
    // 獲取流水線組數量
    int GetGroupCount();
    
    // 獲取管理器名稱
    string GetName();
    
    // 獲取管理器類型
    string GetType();
    
    // 檢查是否已執行
    bool IsExecuted();
    
    // 設置啟用狀態
    void SetEnabled(bool enabled);
    
    // 檢查是否啟用
    bool IsEnabled();
    
    // 清理所有流水線組
    void Clear();
    
    // 獲取所有流水線組
    int GetAllGroups(PipelineGroup* &groups[]);
};
